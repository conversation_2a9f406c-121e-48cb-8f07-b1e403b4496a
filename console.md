---
type: "manual"
---

<div id="errorsList">
        <!--?lit$*********$--><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="0" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="676">
                  <!--?lit$*********$-->⚠️ 未找到字段元素: passportExpiry
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="676" aria-describedby="676" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="0">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="0" tabindex="0" class="selected">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2069 (匿名函数)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="0" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2008 (fillPersonalFields)
                      </li><!----><!---->
                      <li data-frame-index="2" data-error-index="0" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1918 (parsePersonalInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="1" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="677">
                  <!--?lit$*********$-->Element with ID dateOfDeparture not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="677" aria-describedby="677" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="1">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="1" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="2" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="678">
                  <!--?lit$*********$-->Element with ID dateOfArrival not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="678" aria-describedby="678" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="2">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="2" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="3" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="679">
                  <!--?lit$*********$-->Element with ID modeOfTravel not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="679" aria-describedby="679" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="3">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="3" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="4" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="680">
                  <!--?lit$*********$-->Element with ID transportationNo not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="680" aria-describedby="680" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="4">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="4" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="5" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="681">
                  <!--?lit$*********$-->Element with ID accommodationStay not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="681" aria-describedby="681" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="5">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="5" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="6" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="682">
                  <!--?lit$*********$-->Element with ID accommodationState not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="682" aria-describedby="682" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="6">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="6" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="7" aria-expanded="false">
                <cr-icon title="警告">
                </cr-icon>
                <div class="error-message" id="683">
                  <!--?lit$*********$-->Element with ID accommodationCity not found.
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="683" aria-describedby="683" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="7">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2141 (fillTravelFields)
                      </li><!----><!---->
                      <li data-frame-index="1" data-error-index="7" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:1969 (parseTravelInfo)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!----><!---->
          <div class="item-container">
            <div class="cr-row error-item ">
              <div actionable="" class="start" tabindex="0" role="button" data-error-index="8" aria-expanded="false">
                <cr-icon title="错误">
                </cr-icon>
                <div class="error-message" id="675">
                  <!--?lit$*********$-->显示简单数据预览失败: TypeError: this.showSimpleDataPreview is not a function
                </div>
                <div class="cr-icon icon-expand-more">
                </div>
              </div>
              <div class="separator"></div>
              <cr-icon-button class="icon-delete-gray" aria-label="清除此条目" data-error-id="675" aria-describedby="675" role="button" tabindex="0" aria-disabled="false">
              </cr-icon-button>
            </div>
            <cr-collapse role="group" aria-hidden="true" class="collapse-closed" style="max-height: 0px;">
              <div class="devtools-controls">
                <!--?lit$*********$-->
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    上下文
                  </div>
                  <span class="context-url">
                    <!--?lit$*********$-->ui/ui-sidepanel.html
                  </span>
                  <div class="details-heading cr-title-text" role="heading" aria-level="3">
                    堆叠追踪
                  </div>
                  <ul class="stack-trace-container" data-error-index="8">
                    <!--?lit$*********$--><!---->
                      <li data-frame-index="0" data-error-index="8" tabindex="-1" class="">
                        <!--?lit$*********$-->chrome-extension://ojbcjokpcmnigfbbfkdcaomhebokgonp/ui/ui-sidepanel.js:2557 (匿名函数)
                      </li><!---->
                  </ul>
                <extensions-code-section could-not-display-code="这里没有任何通知，往前走吧。">
                </extensions-code-section>
              </div>
            </cr-collapse>
          </div><!---->
      </div>